import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Req, Res,
  UseGuards,
  UsePipes,
  ValidationPipe
} from '@nestjs/common';
import { UserService } from './user.service';
import {SignInDto, SignUpDto} from "./dto/sign.dto";
import {AuthGuard} from "@nestjs/passport";
import { Groups } from '@/api/user/decorators/groups.decorator';
import { JwtAuthGuard } from '@/api/user/guards/auth.guard';
import { AccessGuard } from '@/api/user/guards/access.guard';

@Controller('user')
@Groups('USER_MODERATOR')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get()
  @UseGuards(JwtAuthGuard, AccessGuard)
  async getAll() {
    return await this.userService.getAll()
  }

  @Get('profile')
  @UseGuards(JwtAuthGuard)
  async getProfile(@Req() req) {
    let updateActivity = true
    if(req.headers.referer && typeof req.headers.referer === 'string' && (req.headers.referer.indexOf('9016') !== -1 || req.headers.referer.indexOf('admin') !== -1)) {
      updateActivity = false
    }
    return await this.userService.getProfile(req.user.id, updateActivity);
  }

  @Get('google')
  @UseGuards(AuthGuard('google'))
  async googleAuth(@Req() req) {
    // Сохраняем параметр return в сессии для использования в callback
    if (req.query.return) {
      req.session.returnUrl = req.query.return;
    }
    // Перенаправление на Google OAuth
  }

  @Get('google/callback')
  @UseGuards(AuthGuard('google'))
  async googleAuthRedirect(@Req() req, @Res() res) {
    // Устанавливаем HTTP-куки
    res.cookie('name', req.user.name, {
      httpOnly: false,
    });

    res.cookie('token', req.user.accessToken, {
      httpOnly: false,
    });

    // Получаем сохраненный URL для перенаправления
    const returnUrl = req.session.returnUrl;

    // Определяем базовый URL в зависимости от окружения
    let baseUrl: string;
    switch (process.env.BUILD_ENV) {
      case 'local':
        baseUrl = 'http://localhost:9019';
        break;
      case 'dev':
        baseUrl = 'https://dev.advayta.org';
        break;
      case 'prod':
      case 'production':
        baseUrl = 'https://advayta.org';
        break;
      default:
        baseUrl = 'https://advayta.org'; // fallback to production
    }

    // Очищаем сохраненный URL из сессии
    delete req.session.returnUrl;

    // Редирект на исходную страницу или профиль по умолчанию
    const redirectUrl = returnUrl ? `${baseUrl}${returnUrl}` : `${baseUrl}/ru/profile`;
    return res.redirect(redirectUrl);
  }

  @Post('signin')
  async signIn(@Body() dto: SignInDto) {
    return await this.userService.signIn(dto)
  }

  @Post('signup')
  @UsePipes(new ValidationPipe())
  async signUp(@Body() dto: SignUpDto) {
    return await this.userService.signUp(dto)
  }

  @Patch('profile')
  @UseGuards(JwtAuthGuard)
  async update(@Body() dto: any) {
    return await this.userService.update(dto.id, dto)
  }

  @Delete('profile/favourites/:id')
  @UseGuards(JwtAuthGuard)
  async delete(@Param('id') id: number) {
    return await this.userService.removeFromFavourites(id)
  }

  @Get('groups')
  async getGroups() {
    return await this.userService.getGroups()
  }

  @Get('statuses')
  async getStatuses() {
    return await this.userService.getStatuses()
  }

  @Get('playlist')
  @UseGuards(JwtAuthGuard)
  async getPlaylist(@Req() req) {
    return await this.userService.getPlaylist(req.user.id)
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  async deleteUser(@Param('id') id: number) {
    return await this.userService.deleteUser(id)
  }

  @Get(':id')
  async getOne(@Param('id') id: number) {
    return await this.userService.getOne(id)
  }

  @Post('reset-password')
  async resetPassword(
    @Body('email') email: string
  ) {
    return await this.userService.resetPassword(email)
  }

  @Post('change-password')
  async changePassword(
    @Body() body: { token: string, password: string, confirmPassword: string }
  ) {
    return await this.userService.changePassword(body)
  }
}
