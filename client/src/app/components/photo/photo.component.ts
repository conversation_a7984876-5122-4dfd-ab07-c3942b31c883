import { environment } from "@/env/environment"
import { AuthService } from '@/services/auth.service'
import { PhotoService } from "@/services/photo.service"
import { ProfileService } from "@/services/profile.service"
import { ShareDataService } from '@/services/share-data.service'
import { ToasterService } from '@/services/toaster.service'
import { CommonModule, DOCUMENT, isPlatformBrowser, NgOptimizedImage } from '@angular/common'
import { AfterViewInit, Component, DestroyRef, ElementRef, HostListener, Inject, inject, PLATFORM_ID, QueryList, Renderer2, RendererFactory2, ViewChildren } from '@angular/core'
import { takeUntilDestroyed } from '@angular/core/rxjs-interop'
import { Meta, Title } from "@angular/platform-browser"
import { ActivatedRoute, Router } from "@angular/router"
import { TranslocoService } from "@jsverse/transloco"
import { timer } from 'rxjs'
import { BreadcrumbComponent } from '../breadcrumb/breadcrumb.component'
import { ActionsSettings, PhotoGalleryComponent } from '../photo-gallery/photo-gallery.component'
import { LoadingIndicatorComponent } from '../loading-indicator/loading-indicator.component'

@Component({
  selector: 'photo',
  standalone: true,
  imports: [CommonModule, NgOptimizedImage, BreadcrumbComponent, PhotoGalleryComponent, LoadingIndicatorComponent],
  templateUrl: './photo.component.html',
  styleUrl: './photo.component.scss'
})
export class PhotoComponent implements AfterViewInit {
  @HostListener('document:click', ['$event'])
  @ViewChildren('imageRef') imageRefs!: QueryList<ElementRef<HTMLImageElement>>;
  @ViewChildren('blockRef') blockRefs!: QueryList<ElementRef<HTMLDivElement>>;
  // closeSelectedPhoto(event: Event) {
  //   const targetElement = event.target as HTMLElement;
  //   const selectedPhoto = document.querySelector('.photo-container-item');
  //   if (selectedPhoto && !selectedPhoto.contains(targetElement) && !this.page) {
  //     if (!this.init) {
  //       this.minimizePhoto();
  //     }
  //   }
  // }
  page: boolean = false;
  fast$ = timer(500);
  init: boolean = true;
  profileService = inject(ProfileService);
  photoService = inject(PhotoService);
  shareDataService = inject(ShareDataService);
  authService = inject(AuthService);
  route = inject(ActivatedRoute);
  titleService = inject(Title)
  metaService = inject(Meta)
  router = inject(Router);
  translocoService = inject(TranslocoService)
  auth = inject(AuthService)
  id: any = this.route.snapshot.params['id'];
  renderer;
  folders: boolean = true;
  toasterService = inject(ToasterService);
  selectedPhoto: any;
  selectedPhotoUrl: string = '';
  photos: any = [];
  images: any = [];
  favourites: any = []
  h1: string = ''
  private readonly destroyRef = inject(DestroyRef);
  actionsSettings: ActionsSettings = this.getActionsSettings();
  imageLoadingStates: Record<string, boolean> = {};

  constructor(
    @Inject(PLATFORM_ID) private platformId: Object,
    private rendererFactory: RendererFactory2,
    @Inject(DOCUMENT) private document: Document,
    private renderer2: Renderer2
  ) {
    if (isPlatformBrowser(this.platformId)) {
      this.renderer = this.rendererFactory.createRenderer(null, null);
      // this.renderer.listen('document', 'keydown', (event) => this.handleClick(event));
      this.profileService.getProfile().subscribe();
    }
  }

  ngAfterViewInit() {
    if (this.imageRefs && isPlatformBrowser(this.platformId)) {
      this.imageRefs.forEach((imgRef, index) => {
        if (imgRef.nativeElement.complete) {
          this.setBlockWidth(index);
        } else {
          imgRef.nativeElement.onload = () => this.setBlockWidth(index);
        }
      });
    }
  }



  ngOnInit() {
    if (isPlatformBrowser(this.platformId)) {
      this.photoService.getFavourites().subscribe({
        next: (res: any) => {
          this.favourites = res.map((e: any) => e.id);
        }
      })
    }

    this.translocoService.langChanges$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(lang => {
      this.getDate();
    });

    // Listen for query parameter changes to handle photo parameter
    // this.route.queryParams.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(params => {
    //   const photoId = params['photo'];
    //   if (photoId && this.images && this.images.length > 0) {
    //     this.openPhotoFromUrl(photoId);
    //   }
    // });
  }

  getDate() {
    this.photoService.getAll().subscribe((res: any) => {
      this.photos = res;
      if (this.id) {
        const detail = this.photos.find((e: any) => e.slug == this.id)
        this.images = detail?.photos
        if (detail.seo_title) {
          this.h1 = detail.seo_title;
          this.titleService.setTitle(detail.seo_title);
        }
        if (detail.seo_description) {
          this.metaService.updateTag({ name: 'description', content: detail.seo_description });
        }

        // Check for photo parameter after images are loaded
        // const photoId = this.route.snapshot.queryParams['photo'];
        // if (photoId && this.images && this.images.length > 0) {
        //   this.openPhotoFromUrl(photoId);
        // }
      }
    })
  }

  setBlockWidth(index: number) {
    if (this.imageRefs.length && isPlatformBrowser(this.platformId)) {
      const imgElement = this.imageRefs.toArray()[index]?.nativeElement;
      const blockElement = this.blockRefs.toArray()[index]?.nativeElement;
      if (imgElement && blockElement) {
        blockElement.style.width = `${imgElement.offsetWidth}px`;
      }
    }
  }

  transform(id: number, arr: { id: number }[], itemId = 'id'): boolean {
    if (!this.profileService.profile) {
      return false;
    }
    return arr.some((item: any) => item[itemId] === id);
  }

  favorites(id: number) {
    if (!this.authService.token()) {
      this.shareDataService.redirectToLogin();
      return
    }
    this.photoService.addToFavourites(id).subscribe({
      next: (r) => {
        if (this.inFavourites(id)) {
          this.favourites.splice(this.favourites.indexOf(id), 1)
          this.toasterService.showToast('Фото удалено из избранного!', 'success', 'bottom-middle', 3000);
        } else {
          this.favourites.push(id)
          this.toasterService.showToast('Фото добавленo в избранное!', 'success', 'bottom-middle', 3000);
        }
      },
      error: err => {
        this.toasterService.showToast(err.message, 'error', 'bottom-middle');
      }
    })
  }

  inFavourites(id: number) {
    return this.favourites.includes(id)
  }

    onImageError(photo: any): void {
    photo.imageError = true;
  }

  private getActionsSettings(): ActionsSettings {
    return {
      viewIcon: {
        action: (item: any) => this.redirectToAlbum(item)
      },
      favoritIcon: {
        action: (item: any) => this.favorites(item.id)
      },
      shareIcon: {
        action: (item: any) => this.share(item)
      },
      likeIcon: {
        action: (item: any) => this.like(item)
      }
    };
  }

  share(item: any) {
    if (item && item.name) {
      const url = this.getUrl(item);

      // Try modern clipboard API first
      if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(url)
          .then(() => {
            this.toasterService.showToast('Ссылка скопирована в буфер обмена!', 'success', 'bottom-middle', 3000);
          })
          .catch(err => {
            console.error('Clipboard API failed, trying fallback: ', err);
            this.fallbackShare(url);
          });
      } else {
        // Fallback for browsers that don't support clipboard API
        this.fallbackShare(url);
      }
    }
  }

  private fallbackShare(url: string) {
    // Try Web Share API first (works well on mobile)
    if (navigator.share && isPlatformBrowser(this.platformId)) {
      navigator.share({
        title: 'Фото',
        url: url
      }).then(() => {
        this.toasterService.showToast('Ссылка поделена!', 'success', 'bottom-middle', 3000);
      }).catch(err => {
        console.error('Web Share API failed, trying legacy fallback: ', err);
        this.legacyFallbackCopy(url);
      });
    } else {
      // Legacy fallback using execCommand
      this.legacyFallbackCopy(url);
    }
  }

  private legacyFallbackCopy(url: string) {
    if (!isPlatformBrowser(this.platformId)) {
      this.toasterService.showToast('Не удалось скопировать ссылку', 'error', 'bottom-middle', 3000);
      return;
    }

    try {
      const textArea = document.createElement('textarea');
      textArea.value = url;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';

      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      const successful = document.execCommand('copy');
      document.body.removeChild(textArea);

      if (successful) {
        this.toasterService.showToast('Ссылка скопирована в буфер обмена!', 'success', 'bottom-middle', 3000);
      } else {
        this.showManualCopyOption(url);
      }
    } catch (error) {
      console.error('Legacy copy failed:', error);
      this.showManualCopyOption(url);
    }
  }

  private showManualCopyOption(url: string) {
    // As a last resort, show the URL to the user for manual copying
    this.toasterService.showToast('Скопируйте ссылку: ' + url, 'info', 'bottom-middle', 8000);
  }

  getUrl(item: any) {
    if (item && item.name) {
      const pathMatch = item.name.match(/^(photo\/[^\/]+)/);
      let url = '';

      if (pathMatch && pathMatch[1]) {
        const photoPath = pathMatch[1];
        url = this.environment.baseUrl + '/ru/' + photoPath;
      } else {
        // Use the current route's album slug if available, otherwise use item.slug
        const albumSlug = this.id || item.slug;
        url = this.environment.baseUrl + '/ru/photo/' + albumSlug;
      }

      // Add photo parameter to the URL for direct photo access
      if (item.id) {
        url += '?photo=' + item.id;
      }

      return url;
    }
    return ''
  }

  like(item: any) {
    if (!this.auth.token()) {
      this.shareDataService.redirectToLogin();
      return;
    }

    this.photoService.like(item.id).subscribe(() => {
      item.liked = !item.liked;
      if(item.liked) item.likes++;
      else item.likes--;
    })
  }

  redirectToAlbum(item: any) {
    const url = this.getUrl(item);
    if (url) {
      // Remove the photo parameter to go to the album instead of specific photo
      const urlWithoutPhoto = url.split('?')[0];
      this.router.navigateByUrl(urlWithoutPhoto.replace(this.environment.baseUrl, ''));
    }
  }

  protected readonly environment = environment;

  onPhotoImageLoad(photoId: string) {
    setTimeout(() => {
      this.imageLoadingStates[photoId] = false;
    }, 300);
  }

  onPhotoImageError(photoId: string) {
    this.imageLoadingStates[photoId] = false;
  }
}
