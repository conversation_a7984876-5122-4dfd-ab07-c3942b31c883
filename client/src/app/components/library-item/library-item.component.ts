import {CommonModule, NgClass, NgOptimizedImage} from "@angular/common";
import {Component, ElementRef, EventEmitter, inject, Input, Output, ViewChild} from '@angular/core';
import {Router} from "@angular/router";
import {ProfileService} from "@/services/profile.service";
import {LibraryService} from "@/services/library.service";
import {environment} from "@/env/environment";
import { ToasterService } from "@/services/toaster.service";
import { AuthService } from "@/services/auth.service";
import { ShareDataService } from "@/services/share-data.service";

@Component({
  selector: 'library-item',
  standalone: true,
  imports: [NgClass, CommonModule, NgOptimizedImage],
  templateUrl: './library-item.component.html',
  styleUrl: './library-item.component.scss'
})
export class LibraryItemComponent {
  @Input() item: any
  @Input() quote: string | null = null
  @Input() quoteId: number | null = null
  @Output() deletedAction = new EventEmitter<any>();
  protected readonly environment = environment;
  toasterService = inject(ToasterService);
  authService = inject(AuthService);
  profileService = inject(ProfileService)
  libraryService = inject(LibraryService)
  shareDataService = inject(ShareDataService);
  router = inject(Router)
  @ViewChild('confirmDialog') confirmDialog!: ElementRef<HTMLDialogElement>;
  message: string = "";

  inFavourites(libraryTranslation: any) {
    return this.profileService.profile?.libraryFavourites.find((e: any) => e.id === libraryTranslation.id);
  }

  favorites(item: any) {
    if (!this.authService.token()) {
      this.shareDataService.redirectToLogin();
      return
    }
     this.openConfirmationDialog('Удалить книгу из избранного?').then((confirmed) => {
        if (confirmed) {
          return this.libraryService.addToFavourites(item.id).subscribe({
            next: () => {
              this.deletedAction.emit(item);
              this.toasterService.showToast('Книга удалена из избранного!', 'success', 'bottom-middle', 3000);
            },
            error: err => {
              this.toasterService.showToast(err.message, 'error', 'bottom-middle');
            }
          })
        } else {
          return false;
        }
      });
  }

  like(item: any) {
    if (!this.authService.token()) {
      this.shareDataService.redirectToLogin();
      return
    }
    this.libraryService.like(item.id).subscribe({
      next: () => {
        item.liked = !item.liked;
        if(!item.liked) {
          item.likes--
        } else {
          item.likes++;
        }
        this.toasterService.showToast('Цитата добавлена в понравившееся!', 'success', 'bottom-middle', 3000);
      },
      error: err => {
        this.toasterService.showToast(err.message, 'error', 'bottom-middle');
      }
    })
  }

  isLiked(libraryTranslation: any) {
    return this.profileService.profile?.libraryLikes.find((e: any) => e.id === libraryTranslation.id);
  }

  closeModal(modal: HTMLDialogElement) {
    modal.close();
  }

  openConfirmationDialog(message: string): Promise<boolean> {
    this.message = message;
    this.confirmDialog.nativeElement.showModal();
    return new Promise((resolve) => {
      const okButton = this.confirmDialog.nativeElement.querySelector('.ok-button');
      const cancelButton = this.confirmDialog.nativeElement.querySelector('.cancel-button');
      const closeDialog = (confirmed: boolean) => {
        this.closeModal(this.confirmDialog.nativeElement);
        resolve(confirmed);
      };
      okButton?.addEventListener('click', () => closeDialog(true), { once: true });
      cancelButton?.addEventListener('click', () => closeDialog(false), { once: true });
    });
  }

  goToQuote(code: string, quoteId: number | null) {
    this.router.navigate(['/ru/library/' + code], { queryParams: { quoteId } })
  }

  redirect() {
    this.router.navigate(['/' + this.item.lang + '/library/' + this.item.code]);
  }

  deleteBook() {

  }

  copy() {

      let url = this.environment.baseUrl + '/' + this.item.lang + '/library/' + this.item.code;

      navigator.clipboard.writeText(url)
        .then(() => {
          this.toasterService.showToast('Ссылка скопирована в буфер обмена!', 'success', 'bottom-middle', 3000);
        })
        .catch(err => {
          console.error('Не удалось скопировать ссылку: ', err);
          this.toasterService.showToast('Не удалось скопировать ссылку', 'error', 'bottom-middle', 3000);
        });
  }
}
