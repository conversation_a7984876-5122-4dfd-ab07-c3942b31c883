import {Component, ElementRef, inject, ViewChild} from "@angular/core";
import {CommonModule, NgOptimizedImage} from "@angular/common";
import {environment} from "@/env/environment";
import {ToasterService} from "@/services/toaster.service";
import {Router} from "@angular/router";
import {ContentService} from "@/services/content.service";
import {AuthService} from "@/services/auth.service";
import {ShareDataService} from "@/services/share-data.service";

@Component({
  selector: 'ContentFavourites',
  standalone: true,
  imports: [CommonModule, NgOptimizedImage],
  templateUrl: './content-favourites.component.html',
  styleUrl: '../favourites.component.scss'
})
export class ContentFavouritesComponent {
  @ViewChild('confirmDialog') confirmDialog!: ElementRef<HTMLDialogElement>;
  @ViewChild('modal') modal!: ElementRef<HTMLDialogElement>;
  toasterService = inject(ToasterService);
  contentService = inject(ContentService);
  auth = inject(AuthService)
  shareDataService = inject(ShareDataService);
  router = inject(Router);
  message: string = "";
  selectedDropdElement: any = null;
  quoteActions = [
    'копировать',
    'удалить',
    'поделиться',
    'мне нравится',
  ];

  items: any = []
  page: number = 1;
  totalPages = 1;

  ngOnInit() {
    this.get()
  }

  get() {
    this.contentService.getFavourites().subscribe((res: any) => {
      this.contentService.getFavouritesByIds(res, this.page).subscribe((res: any) => {
        this.totalPages = res.pagination.totalPages;
        this.items = [...this.items, ...res.items]
      })
    })
  }

  closeModal(modal: HTMLDialogElement) {
    modal.close();
  }

  redirect(item: any) {
    this.router.navigateByUrl('/ru/categories/' + item.category.id + '/' + item.slug);
  }

  closeMobileActionSelect() {
    this.selectedDropdElement = null;
  }

  showMobileActionOptions(quote: any) {
    this.selectedDropdElement = quote;
  }

  onClickMobileAction() {
    this.closeMobileActionSelect();
  }

  share(content: any) {
    let url = this.environment.baseUrl + '/' + content.lang + '/categories/' + content.category.id + '/' + content.slug;
    navigator.clipboard.writeText(url)
      .then(() => {
        this.toasterService.showToast('Ссылка скопирована в буфер обмена!', 'success', 'bottom-middle', 3000);
      })
      .catch(err => {
        console.error('Не удалось скопировать ссылку: ', err);
        this.toasterService.showToast('Не удалось скопировать ссылку', 'error', 'bottom-middle', 3000);
      });
  }

  favorites(id: number) {
    this.contentService.addToFavourites(id).subscribe(() => {
      const index = this.items.findIndex((item: any) => item.id === id);
      this.items.splice(index, 1)
      this.toasterService.showToast('Статья удалена из избранного!', 'error', 'bottom-middle', 3000);
    });
  }

  like(content: any) {
    if (!this.auth.token()) {
      this.shareDataService.redirectToLogin();
      return;
    }

    this.contentService.like(content.id).subscribe({
      next: (r) => {

        content.liked = !content.liked;
        if(content.liked) content.likes++
        else content.likes--;

        if(!r) {
          this.toasterService.showToast('Статья добавлена в понравившееся!', 'success', 'bottom-middle', 3000);
        } else {
          this.toasterService.showToast('Статья удалена из понравившихся!', 'success', 'bottom-middle', 3000);
        }
      },
      error: err => {
        this.toasterService.showToast(err.message, 'error', 'bottom-middle');
      }
    });
  }

  protected readonly environment = environment;
}
