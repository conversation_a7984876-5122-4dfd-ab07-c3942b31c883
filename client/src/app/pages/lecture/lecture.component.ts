import { AudioPlayerComponent } from "@/components/audio-player/audio-player.component";
import { Component, inject, signal, OnInit, PLATFORM_ID, ViewChild, effect } from '@angular/core';
import { <PERSON><PERSON>anitizer, Meta, SafeResourceUrl, Title } from '@angular/platform-browser';
import { CommonModule, isPlatformBrowser, NgOptimizedImage } from '@angular/common';
import { AudioService } from "@/services/audio.service";
import { ActivatedRoute, Router } from '@angular/router';
import { ToasterService } from "@/services/toaster.service";
import { BreadcrumbComponent } from "@/components/breadcrumb/breadcrumb.component";
import { TranslocoService } from "@jsverse/transloco";
import { RouterLink } from '@angular/router';
import { ShareDataService } from "@/services/share-data.service";
import { PlaylistDialogComponent } from "../audio-gallery/playlist-dialog/playlist-dialog.component";
import { AuthService } from "@/services/auth.service";
import { IsInPlaylistPipe } from "@/pipes/isInPlaylist.pipe";
import { ProfileService } from "@/services/profile.service";
import { FavoritesIconComponent } from "@/components/svg-components/favorites-icon/favorites-icon.component";

enum LectureTab {
  AUDIO = 'Аудиолекция',
  VIDEO = 'Видеолекция',
}

@Component({
  selector: 'app-lecture',
  standalone: true,
  imports: [
    AudioPlayerComponent,
    CommonModule,
    BreadcrumbComponent,
    RouterLink,
    NgOptimizedImage,
    PlaylistDialogComponent,
    IsInPlaylistPipe,
    FavoritesIconComponent
],
  templateUrl: './lecture.component.html',
  styleUrl: './lecture.component.scss'
})
export class LectureComponent implements OnInit {
  translocoService = inject(TranslocoService)
  router = inject(Router);
  audioService = inject(AudioService)
  sanitizer = inject(DomSanitizer)
  authService = inject(AuthService);
  profileService = inject(ProfileService);
  route = inject(ActivatedRoute)
  forceToPlay: boolean = false;
  platformId = inject(PLATFORM_ID)
  sanitizedVideoUrl!: SafeResourceUrl;
  titleService = inject(Title);
  shareDataService = inject(ShareDataService);
  metaService = inject(Meta);
  paramValue: string | null = null;
  toasterService = inject(ToasterService);
  lectureTabs: LectureTab[] = [LectureTab.AUDIO, LectureTab.VIDEO];
  activeTab: LectureTab = this.lectureTabs[0];
  track = signal<any>(null);
  similar: any = [];
  LectureTab = LectureTab;
  // similarAudioShowLast: boolean = false;
  showPlaylist = false;
  playlists = []
  selectedTrackId = -1
  protected readonly Math = Math;

  @ViewChild(AudioPlayerComponent) audioPlayerComponent!: AudioPlayerComponent;

  ngOnInit() {
    this.route.paramMap.subscribe(params => {
      if (isPlatformBrowser(this.platformId)) {
        window.scrollTo({ top: 0, behavior: 'smooth' });
      }
      this.paramValue = params.get('param');
      if (this.paramValue) {
        this.audioService.get(this.paramValue).subscribe((res: any) => {

          this.track.set(res);
          this.selectedTrackId = res.id;
          if (isPlatformBrowser(this.platformId))
            // this.updateAudioDate();
          if (this.authService.isAuth) {
            this.profileService.getPlaylist().subscribe((res: any) => this.playlists = res);
          }
          if (this.track().youtube) {
            const link = this.createEmbedLink(this.track().youtube);
            this.sanitizedVideoUrl = this.sanitizeUrl(link);
          }
          this.titleService.setTitle(this.track().seo_title || 'Лекция Свами Вишнудевананда Гири');
          this.metaService.updateTag({ name: 'description', content: (this.track().seo_description || 'Лекция Свами Вишнудевананда Гири') });
          this.audioService.getSimilar(this.paramValue!).subscribe((res: any) => this.similar = res)
        })
      }
    });

    this.route.queryParamMap.subscribe(queryParams => {
      const tabParam = queryParams.get('tab');
      if(tabParam == 'video') {
        this.activeTab =  this.lectureTabs[1];
      }

    });

  }

  sanitizeUrl(url: string): SafeResourceUrl {
    return this.sanitizer.bypassSecurityTrustResourceUrl(url);
  }

  selectTab(tab: LectureTab): void {
    this.activeTab = tab;
  }

  createEmbedLink(youtubeUrl: string): string {
    // Handle both youtube.com and youtu.be formats
    const patterns = {
      long: /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/,
      short: /youtu\.be\/([^"&?\/\s]{11})/
    };

    const match = youtubeUrl.match(patterns.long) || youtubeUrl.match(patterns.short);

    if (match && match[1]) {
      const videoId = match[1];
      return `https://www.youtube.com/embed/${videoId}`;
    }

    return youtubeUrl; // Return original URL if no match found instead of throwing error
  }

  moveLeft() {
    if (isPlatformBrowser(this.platformId)) {
      document.getElementById('carousel')?.scrollBy({
        left: -260,
        behavior: 'smooth'
      });
    }
  }

  moveRight() {
    if (isPlatformBrowser(this.platformId)) {
      document.getElementById('carousel')?.scrollBy({
        left: 260,
        behavior: 'smooth'
      });
    }
  }

  like() {
    this.audioService.like(this.track().id).subscribe((r) => {
      if ((r as any).audio) {
        this.track().liked = true;
        this.track().likes += 1;
        this.toasterService.showToast('Лекция добавлена в понравившееся!', 'success', 'bottom-middle', 3000);
      } else {
        this.track().liked = false;
        this.track().likes -= 1;

        this.toasterService.showToast('Лекция удалена из понравившихся!', 'success', 'bottom-middle', 3000);
      }
    })
  }

  addToFavourites() {
    this.audioService.addToFavourites(this.track().id).subscribe((r) => {
      if ((r as any).audio) {
        this.track().inFavourites = true;
        this.toasterService.showToast('Лекция добавлена в избранное!', 'success', 'bottom-middle', 3000);
      } else {
        this.track().inFavourites = false;
        this.toasterService.showToast('Лекция удалена из избранного!!', 'success', 'bottom-middle', 3000);
      }
    });
  }

  updateAudioDate() {
    if (this.authService.isAuth) {
      this.audioService.get(this.track().external_id, false).subscribe((res: any) => {
        this.track.set(res);
      })
    }
  }

  downloadText(event: Event) {
    event.stopPropagation();
    event.preventDefault();
    if (isPlatformBrowser(this.platformId)) {
      this.audioService.download(this.track().textLink).subscribe(blob => {
        const downloadURL = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = downloadURL;
        link.download = this.track().title + '.txt';
        link.click();
        window.URL.revokeObjectURL(downloadURL);
        link.remove();
      })
    }
  }

  goToListByTag(id: any) {
    this.router.navigate(['/' + this.translocoService.getActiveLang() + '/audiogallery/audiolektsii'], {
      queryParams: { tags: [id] }
    });
  }

  goToAnotherAudio(id: any) {
    this.forceToPlay = !this.forceToPlay
    this.router.navigate(['/' + this.translocoService.getActiveLang() + '/audiogallery/audiolektsii/' + id]);
  }

  getAbsolutePath(url: string): string {
    if (!url) return '';

    try {
      // Extract the path from the full URL
      const urlObj = new URL(url);
      // Return the pathname as a string (preserving the leading slash)
      return urlObj.pathname;
    } catch (e) {
      console.error('Invalid URL:', url);
      return '';
    }
  }

  share(): void {
    if (this.audioPlayerComponent) {
      this.audioPlayerComponent.share();
    }
  }

  addToQueue(): void {
    if(!this.audioService.hasAccess(this.track())) {
      this.toasterService.showToast('Платный материал, оформите подписку', 'error', 'bottom-middle', 3000)
      return;
    }

    this.shareDataService.addToPlaylist(this.track());
    this.toasterService.showToast('Лекция добавлена в очередь', 'success', 'bottom-middle', 3000);
  }

  showPlaylistDialog() {
    if (!this.authService.token()) {
      this.shareDataService.redirectToLogin();
      return
    }
    this.showPlaylist = true;

    this.selectedTrackId = this.track().id;
  }

  playlistClosed(event: any) {
    this.showPlaylist = event;
  }

  playlistSelected(playlists: any) {
    this.showPlaylist = false;

    this.playlists = playlists;
  }

  // showSimilarElement(elementType: string): void {
  //   this.similarAudioShowLast = elementType === 'last';
  // }
}
