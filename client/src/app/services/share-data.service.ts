import { Injectable, inject } from '@angular/core';
import { Track } from '@/interfaces/track';
import { BehaviorSubject } from 'rxjs';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class ShareDataService {
  private router = inject(Router);

  addToPlaylist$: BehaviorSubject<any> = new BehaviorSubject<any>(null);
  playCard$: BehaviorSubject<any> = new BehaviorSubject<any>(null);
  playMainPlayer$: BehaviorSubject<any> = new BehaviorSubject<any>(null);
  playRadio$: BehaviorSubject<any> = new BehaviorSubject<any>(null);
  changeTracks$: BehaviorSubject<any> = new BehaviorSubject<null>(null);
  showInfoModal$: BehaviorSubject<any> = new BehaviorSubject<null>(null);

  addToPlaylist(track: Track, play?: boolean) {
    this.addToPlaylist$.next({track, play});
  }

  playCard() {
    this.playCard$.next(true);
  }

  playMainPlayer() {
    this.playMainPlayer$.next(true);
  }

  playRadio(track: any, play?: boolean) {
    let radio: Track = {
      link: track,
      audioStatus: '',
      author: 'Радио',
      comment: 'Радио',
      date: '',
      description: 'Радио',
      duration: '∞',
      fullDescription: '',
      external_id: '',
      scriptures: {},
      title: 'Радио',
      type: '',
      videoStatus: 'false',
      youtube: '',
      views: 0,
      likes: 0,
      id: 235435657548,
      liked: false,
      inFavourites: false,
  }
    this.playRadio$.next({radio, play});
  }

  showInfoModal(message: string) {
    this.showInfoModal$.next(message);
  }

  // Новый метод для перенаправления на страницу входа с сохранением текущего URL
  redirectToLogin() {
    const currentUrl = this.router.url;
    this.router.navigate(['/ru/signin'], { queryParams: { return: currentUrl } });
  }

  changePlaylist(items: Track[]) {
    this.changeTracks$.next(items);
  }
}
